// NocoDB API service
const NOCODB_BASE_URL = import.meta.env.VITE_NOCODB_BASE_URL || 'https://your-nocodb-instance.com';
const NOCODB_API_TOKEN = import.meta.env.VITE_NOCODB_API_TOKEN || 'your-api-token';
const NOCODB_BASE_ID = import.meta.env.VITE_NOCODB_BASE_ID || 'your-base-id';

// Table IDs
const FOUNDERS_TABLE_ID = import.meta.env.VITE_NOCODB_FOUNDERS_TABLE_ID || 'founders-table-id';
const INVESTORS_TABLE_ID = import.meta.env.VITE_NOCODB_INVESTORS_TABLE_ID || 'investors-table-id';

interface NocoDBResponse {
  success: boolean;
  data?: any;
  error?: string;
}

class NocoDBService {
  private baseURL: string;
  private headers: HeadersInit;

  constructor() {
    this.baseURL = `${NOCODB_BASE_URL}/api/v2/tables`;
    this.headers = {
      'Content-Type': 'application/json',
      'xc-token': NOCODB_API_TOKEN,
    };
  }

  async submitFounderData(data: any): Promise<NocoDBResponse> {
    try {
      const response = await fetch(`${this.baseURL}/${FOUNDERS_TABLE_ID}/records`, {
        method: 'POST',
        headers: this.headers,
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return { success: true, data: result };
    } catch (error) {
      console.error('Error submitting founder data:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  async submitInvestorData(data: any): Promise<NocoDBResponse> {
    try {
      const response = await fetch(`${this.baseURL}/${INVESTORS_TABLE_ID}/records`, {
        method: 'POST',
        headers: this.headers,
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return { success: true, data: result };
    } catch (error) {
      console.error('Error submitting investor data:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  async getFounders(limit = 50, offset = 0): Promise<NocoDBResponse> {
    try {
      const response = await fetch(
        `${this.baseURL}/${FOUNDERS_TABLE_ID}/records?limit=${limit}&offset=${offset}`,
        {
          method: 'GET',
          headers: this.headers,
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return { success: true, data: result };
    } catch (error) {
      console.error('Error fetching founders:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  async getInvestors(limit = 50, offset = 0): Promise<NocoDBResponse> {
    try {
      const response = await fetch(
        `${this.baseURL}/${INVESTORS_TABLE_ID}/records?limit=${limit}&offset=${offset}`,
        {
          method: 'GET',
          headers: this.headers,
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return { success: true, data: result };
    } catch (error) {
      console.error('Error fetching investors:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }
}

export const nocoDBService = new NocoDBService();
export default NocoDBService;
