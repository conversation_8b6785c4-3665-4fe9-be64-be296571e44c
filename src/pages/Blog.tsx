import { useTranslation } from 'react-i18next';
import Header from '@/components/Header';

const Blog = () => {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {t('blog.title', 'Blog')}
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            {t('blog.subtitle', 'Insights, trends, and stories from the world of early-stage investing')}
          </p>
        </div>

        <div className="text-center py-20">
          <div className="max-w-md mx-auto">
            <div className="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
              <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
              </svg>
            </div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              {t('blog.comingSoon', 'Coming Soon')}
            </h2>
            <p className="text-gray-600 mb-8">
              {t('blog.comingSoonDescription', 'We\'re working on bringing you the latest insights and stories from the investment world. Stay tuned!')}
            </p>
            <a
              href="/"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-[#40826D] hover:bg-[#2D5A4A] transition-colors"
            >
              {t('blog.backToHome', 'Back to Home')}
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Blog;
