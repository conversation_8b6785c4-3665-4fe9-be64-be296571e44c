// Test submission utilities for founder and investor forms

export interface TestFounderData {
  founder_name: string;
  founder_email: string;
  founder_linkedin_url: string;
  founder_github_url: string;
  founder_twitter_url: string;
  founder_location: string;
  project_name: string;
  project_website_url: string;
  project_oneliner: string;
  project_description: string;
  project_stage: string;
  industry_verticals: string[];
  current_traction_data: string;
  funding_needs: string;
  source: string;
  founder_wechat_id_cn: string;
  project_wechat_official_account_cn: string;
  project_showcase_url_cn: string;
  developer_community_url_cn: string;
}

export interface TestInvestorData {
  investor_name: string;
  investor_email: string;
  investor_linkedin_url: string;
  firm_name: string;
  firm_website_url: string;
  investor_type: string;
  investment_stages: string[];
  preferred_verticals: string[];
  geography_preference: string[];
  average_check_size: string;
  thesis_keywords: string;
  source: string;
  investor_wechat_id_cn: string;
  investor_wechat_official_account: string;
  focus_on_chuhai_cn: boolean;
}

// Test data for founder form
export const testFounderData: TestFounderData = {
  founder_name: "张伟",
  founder_email: "<EMAIL>",
  founder_linkedin_url: "https://linkedin.com/in/zhangwei",
  founder_github_url: "https://github.com/zhangwei",
  founder_twitter_url: "https://twitter.com/zhangwei",
  founder_location: "北京, 中国",
  project_name: "智能医疗助手",
  project_website_url: "https://smartmedical.ai",
  project_oneliner: "基于AI的智能医疗诊断助手，帮助医生提高诊断准确率",
  project_description: "我们开发了一个基于深度学习的医疗诊断助手，能够分析医学影像和患者症状，为医生提供准确的诊断建议。我们的AI模型在多个医学数据集上达到了95%以上的准确率，已经在3家三甲医院进行试点应用。",
  project_stage: "early_traction",
  industry_verticals: ["ai", "healthcare", "enterprise"],
  current_traction_data: "已获得3家三甲医院试点合作，月活跃医生用户200+，诊断准确率提升15%",
  funding_needs: "raising_funds",
  source: "Founder Form",
  founder_wechat_id_cn: "zhangwei_ai",
  project_wechat_official_account_cn: "智能医疗助手",
  project_showcase_url_cn: "https://smartmedical.cn",
  developer_community_url_cn: "https://dev.smartmedical.cn"
};

// Test data for investor form
export const testInvestorData: TestInvestorData = {
  investor_name: "李明",
  investor_email: "<EMAIL>",
  investor_linkedin_url: "https://linkedin.com/in/liming",
  firm_name: "创新资本",
  firm_website_url: "https://innovationcapital.com",
  investor_type: "venture_capital",
  investment_stages: ["seed", "series_a"],
  preferred_verticals: ["ai", "healthcare", "fintech"],
  geography_preference: ["china_mainland", "greater_china"],
  average_check_size: "$250k - $1M",
  thesis_keywords: "人工智能, 医疗健康, 企业服务, 早期投资",
  source: "Investor Form",
  investor_wechat_id_cn: "liming_vc",
  investor_wechat_official_account: "创新资本",
  focus_on_chuhai_cn: true
};

// Function to submit test founder data
export const submitTestFounderData = async (): Promise<boolean> => {
  try {
    console.log('Submitting test founder data:', testFounderData);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Log success
    console.log('✅ Test founder submission successful!');
    console.log('Data submitted:', JSON.stringify(testFounderData, null, 2));
    
    return true;
  } catch (error) {
    console.error('❌ Test founder submission failed:', error);
    return false;
  }
};

// Function to submit test investor data
export const submitTestInvestorData = async (): Promise<boolean> => {
  try {
    console.log('Submitting test investor data:', testInvestorData);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Log success
    console.log('✅ Test investor submission successful!');
    console.log('Data submitted:', JSON.stringify(testInvestorData, null, 2));
    
    return true;
  } catch (error) {
    console.error('❌ Test investor submission failed:', error);
    return false;
  }
};

// Function to validate founder form data
export const validateFounderData = (data: Partial<TestFounderData>): string[] => {
  const errors: string[] = [];
  
  if (!data.founder_name) errors.push('Founder name is required');
  if (!data.founder_email) errors.push('Founder email is required');
  if (!data.project_name) errors.push('Project name is required');
  if (!data.project_oneliner) errors.push('Project one-liner is required');
  if (!data.project_description) errors.push('Project description is required');
  if (!data.project_stage) errors.push('Project stage is required');
  if (!data.industry_verticals || data.industry_verticals.length === 0) {
    errors.push('At least one industry vertical is required');
  }
  if (!data.funding_needs) errors.push('Funding needs is required');
  
  return errors;
};

// Function to validate investor form data
export const validateInvestorData = (data: Partial<TestInvestorData>): string[] => {
  const errors: string[] = [];
  
  if (!data.investor_name) errors.push('Investor name is required');
  if (!data.investor_email) errors.push('Investor email is required');
  if (!data.investor_type) errors.push('Investor type is required');
  if (!data.investment_stages || data.investment_stages.length === 0) {
    errors.push('At least one investment stage is required');
  }
  if (!data.preferred_verticals || data.preferred_verticals.length === 0) {
    errors.push('At least one preferred vertical is required');
  }
  if (!data.geography_preference || data.geography_preference.length === 0) {
    errors.push('At least one geography preference is required');
  }
  if (!data.average_check_size) errors.push('Average check size is required');
  
  return errors;
};
