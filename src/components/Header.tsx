
import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import LanguageSwitcher from '@/components/LanguageSwitcher';

const Header = () => {
  const location = useLocation();
  const { t } = useTranslation();

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <header className="bg-gradient-to-r from-[#40826D] via-[#4a9d7a] to-[#40826D] shadow-lg sticky top-0 z-50 relative overflow-hidden">
      {/* Crystal glow effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse"></div>
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(255,255,255,0.1)_0%,transparent_70%)]"></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="flex justify-between items-center py-4">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 flex items-center justify-center">
              <img
                src="/logo.png"
                alt="Veridian Vista Logo"
                className="w-10 h-10 object-contain drop-shadow-lg"
                onError={(e) => {
                  // Fallback to a simple text logo if image fails to load
                  e.currentTarget.style.display = 'none';
                  e.currentTarget.parentElement!.innerHTML = '<div class="text-white font-bold text-xl">VV</div>';
                }}
              />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">
                Veridian Vista
              </h1>
            </div>
          </div>
          
          <nav className="hidden md:flex space-x-8 items-center">
            <Link
              to="/"
              className={`font-medium transition-colors ${
                isActive('/')
                  ? 'text-white'
                  : 'text-white/80 hover:text-white'
              }`}
            >
              {t('header.home')}
            </Link>

            <Link
              to="/founder"
              className={`font-medium transition-colors ${
                isActive('/founder')
                  ? 'text-white'
                  : 'text-white/80 hover:text-white'
              }`}
            >
              {t('header.founder')}
            </Link>

            <Link
              to="/investor"
              className={`font-medium transition-colors ${
                isActive('/investor')
                  ? 'text-white'
                  : 'text-white/80 hover:text-white'
              }`}
            >
              {t('header.investor')}
            </Link>

            <Link
              to="/about"
              className={`font-medium transition-colors ${
                isActive('/about')
                  ? 'text-white'
                  : 'text-white/80 hover:text-white'
              }`}
            >
              {t('header.about')}
            </Link>
          </nav>

          <div className="flex items-center space-x-3">
            <LanguageSwitcher />
            <Button
              className="bg-white text-[#40826D] hover:bg-gray-100 font-medium px-6 py-2 whitespace-nowrap"
              onClick={() => {
                const contactSection = document.querySelector('footer');
                if (contactSection) {
                  contactSection.scrollIntoView({ behavior: 'smooth' });
                }
              }}
            >
              {t('homepage.hero.joinUs', 'Join Us')}
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
